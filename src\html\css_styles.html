<style>
/**
 * Styles principaux pour SIGMA
 */

/* Variables globales */
:root {
  --primary-color: #3f51b5;
  --secondary-color: #f50057;
  --background-color: #f5f5f5;
  --card-background: #ffffff;
  --text-color: #333333;
  --text-light: #777777;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
  --border-radius: 4px;
  --box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Styles de base */
body {
  font-family: '<PERSON>o', Arial, sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  margin: 0;
  padding: 0;
  line-height: 1.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* En-têtes */
h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  font-weight: 500;
  line-height: 1.2;
}

h1 {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

h2 {
  font-size: 1.5rem;
  margin-bottom: 0.75rem;
}

/* Cartes du Dashboard */
.dashboard-card {
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  margin-bottom: 20px;
  overflow: hidden;
}

.dashboard-card h2 {
  background-color: var(--primary-color);
  color: white;
  margin: 0;
  padding: 10px 15px;
  font-size: 1.2rem;
}

.card-content {
  padding: 15px;
}

/* Tableaux */
table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
}

th, td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

th {
  background-color: #f5f5f5;
  font-weight: 500;
}

tr:hover {
  background-color: #f9f9f9;
}

/* États et alertes */
.status-ready {
  color: var(--success-color);
}

.status-not-ready {
  color: var(--error-color);
}

.status-warning {
  color: var(--warning-color);
}

/* Notifications */
.notification {
  padding: 10px 15px;
  border-radius: var(--border-radius);
  margin-bottom: 15px;
  position: relative;
}

.notification-info {
  background-color: #e3f2fd;
  border-left: 4px solid #2196f3;
}

.notification-success {
  background-color: #e8f5e9;
  border-left: 4px solid var(--success-color);
}

.notification-warning {
  background-color: #fff3e0;
  border-left: 4px solid var(--warning-color);
}

.notification-error {
  background-color: #ffebee;
  border-left: 4px solid var(--error-color);
}

/* Navigation */
.main-nav {
  background-color: var(--primary-color);
  color: white;
  padding: 0;
}

.main-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
}

.main-nav li {
  padding: 0;
}

.main-nav .nav-link {
  color: white;
  text-decoration: none;
  padding: 15px 20px;
  display: block;
  transition: background-color 0.2s;
}

.main-nav .nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.main-nav .nav-link.active {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Bouton de déconnexion */
#direct-logout {
  margin-left: auto; /* Pousse le bouton à droite */
}

#direct-logout-link {
  background-color: rgba(244, 67, 54, 0.1); /* Fond rouge transparent */
  font-weight: bold;
  border-radius: 4px;
  margin: 8px 10px;
  padding: 7px 15px;
}

#direct-logout-link:hover {
  background-color: var(--error-color);
  color: white;
}

/* Pied de page */
.main-footer {
  background-color: #333;
  color: white;
  padding: 20px 0;
  margin-top: 40px;
}

/* Boutons */
.btn {
  display: inline-block;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: var(--border-radius);
  transition: all 0.15s ease-in-out;
  cursor: pointer;
}

.btn-primary {
  color: #fff;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-secondary {
  color: #fff;
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.btn-success {
  color: #fff;
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.btn-warning {
  color: #212529;
  background-color: var(--warning-color);
  border-color: var(--warning-color);
}

.btn-danger {
  color: #fff;
  background-color: var(--error-color);
  border-color: var(--error-color);
}

/* Styles pour les notifications */
#notifications {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  max-width: 300px;
}

.notification {
  margin-bottom: 10px;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
  animation: fade-in 0.3s ease-out;
}

.notification-close {
  position: absolute;
  top: 5px;
  right: 5px;
  background: transparent;
  border: none;
  color: inherit;
  font-size: 16px;
  cursor: pointer;
  opacity: 0.7;
}

.notification-close:hover {
  opacity: 1;
}

.notification.fade-out {
  animation: fade-out 0.5s ease-out forwards;
}

@keyframes fade-in {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fade-out {
  from { opacity: 1; transform: translateY(0); }
  to { opacity: 0; transform: translateY(-10px); }
}

/* Styles pour la page de connexion */
.login-page {
  background-color: var(--background-color);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-container {
  max-width: 400px;
  width: 100%;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-header h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  color: var(--primary-color);
}

.login-form .card {
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.login-form .card-body {
  padding: 2rem;
}

#google-signin {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  font-size: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
  .main-nav ul {
    flex-direction: column;
  }

  .container {
    padding: 0 10px;
  }

  h1 {
    font-size: 1.5rem;
  }

  h2 {
    font-size: 1.2rem;
  }

  #notifications {
    max-width: 90%;
    right: 5%;
  }

  .login-container {
    max-width: 90%;
  }

  .login-form .card-body {
    padding: 1.5rem;
  }
}
</style>
