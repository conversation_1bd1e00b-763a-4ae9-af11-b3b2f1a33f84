<!DOCTYPE html>
<html>
  <head>
    <base target="_top">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>SIGMA - Maintenance</title>
    
    <!-- Inclure les styles -->
    <?!= include('css_styles'); ?>
    
    <style>
      .maintenance-container {
        text-align: center;
        margin-top: 50px;
        padding: 20px;
      }
      
      .maintenance-icon {
        font-size: 5rem;
        margin-bottom: 20px;
        color: var(--primary-color);
      }
      
      .maintenance-title {
        font-size: 2rem;
        margin-bottom: 20px;
        color: var(--primary-color);
      }
      
      .maintenance-message {
        font-size: 1.2rem;
        margin-bottom: 20px;
        color: var(--text-color);
      }
      
      .refresh-button {
        padding: 10px 20px;
        background-color: var(--primary-color);
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 1rem;
      }
      
      .refresh-button:hover {
        background-color: #2c3e9b;
      }
    </style>
  </head>
  <body>
    <div class="maintenance-container">
      <div class="maintenance-icon">🛠️</div>
      <h1 class="maintenance-title">Maintenance en cours</h1>
      <p class="maintenance-message">
        L'application SIGMA est actuellement en maintenance.<br>
        Nous nous excusons pour ce désagrément et vous remercions de votre patience.
      </p>
      <p class="maintenance-message">
        L'application sera de nouveau disponible dès que possible.
      </p>
      <button class="refresh-button" onclick="window.location.reload()">
        Rafraîchir la page
      </button>
    </div>
  </body>
</html>
