/**
 * Point d'entrée principal de l'application SIGMA
 *
 * Ce fichier contient:
 * - La fonction doGet qui génère l'interface utilisateur
 * - La fonction include pour intégrer des fichiers HTML
 * - La gestion sécurisée de la configuration Firebase via Properties Service
 */

/**
 * Fonction principale qui génère l'interface web.
 * Elle sert les pages HTML depuis le dossier /src/html/ en se basant sur le paramètre d'URL 'page'.
 * @param {Object} e - L'objet événement de la requête, contenant les paramètres d'URL.
 * @returns {HtmlService.HtmlOutput} L'interface HTML à afficher.
 */
function doGet(e) {
  try {
    // Log initial pour le débogage
    console.log('doGet appelé avec paramètres:', e ? JSON.stringify(e) : 'aucun paramètre');

    // 1. Vérifier si l'application est en mode maintenance
    if (isMaintenanceMode()) {
      console.log('Application en mode maintenance.');
      // Si oui, servir la page de maintenance et arrêter l'exécution.
      // On suppose que maintenance.html est aussi dans le dossier /html/
      return HtmlService.createTemplateFromFile('html/maintenance')
        .evaluate()
        .setTitle('SIGMA - Maintenance')
        .addMetaTag('viewport', 'width=device-width, initial-scale=1')
        .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
    }

    // 2. Déterminer la page à servir
    let pageToServe = 'html/index'; // Page par défaut si aucun paramètre n'est fourni

    if (e && e.parameter && e.parameter.page) {
      // Sécuriser le nom de la page demandée pour éviter les injections de caractères malveillants
      const sanitizedPageName = e.parameter.page.replace(/[^a-zA-Z0-9_-]/g, '');

      // Construire le chemin du fichier en assumant que toutes les pages sont dans /src/html/
      // CONVENTION: un underscore '_' dans le nom de la page est remplacé par un slash '/'
      // pour gérer les sous-dossiers. Ex: une requête pour 'admin_users' cherchera le fichier 'html/admin/users.html'.
      const potentialPath = 'html/' + sanitizedPageName.replace('_', '/');

      // 3. Valider si le fichier de la page demandée existe réellement dans le projet
      try {
        HtmlService.createTemplateFromFile(potentialPath); // Tente de charger le fichier pour vérifier son existence
        pageToServe = potentialPath; // Si le chargement réussit, c'est la page que nous allons servir
        console.log(`Page valide demandée: '${sanitizedPageName}'. Chemin servi: '${pageToServe}'`);
      } catch (error) {
        // Si le fichier n'existe pas, on renvoie une page d'erreur 404 claire à l'utilisateur.
        console.error(`Page demandée introuvable. Nom: '${sanitizedPageName}', Chemin tenté: '${potentialPath}'. Erreur: ${error.message}`);
        return createErrorPage(
          'Page non trouvée',
          `La page demandée ('${sanitizedPageName}') n'existe pas ou n'a pas pu être chargée.`,
          'Retourner à l\'accueil'
        );
      }
    }

    // 4. Servir la page déterminée (soit la page par défaut, soit celle demandée et validée)
    console.log(`Accès à l'application - Page finale servie: ${pageToServe}`);
    const template = HtmlService.createTemplateFromFile(pageToServe);
    const htmlOutput = template.evaluate();

    return htmlOutput
      .setTitle('SIGMA - Système Informatique de Gestion du Matériel')
      .addMetaTag('viewport', 'width=device-width, initial-scale=1')
      .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);

  } catch (error) {
    // Gestion des erreurs globales (par exemple, si la page par défaut 'html/index.html' est introuvable)
    console.error(`Erreur critique dans doGet: ${error.stack}`);
    return createErrorPage(
      'Erreur Système',
      'Une erreur inattendue est survenue lors du chargement de l\'application.',
      'Retourner à l\'accueil'
    );
  }
}


/**
 * Fonction pour inclure des fichiers HTML (wrappers pour CSS/JS).
 * @param {string} filename - Nom du fichier à inclure.
 * @return {string} Contenu du fichier.
 */
function include(filename) {
  try {
    // Log pour le débogage
    console.log(`Tentative d'inclusion du fichier: ${filename}`);
    return HtmlService.createHtmlOutputFromFile(filename).getContent();
  } catch (error) {
    console.error(`Erreur lors de l'inclusion du fichier ${filename}:`, error);
    return `<!-- Erreur: Impossible d'inclure le fichier ${filename} -->`;
  }
}

/**
 * Vérifie si l'application est en mode maintenance.
 * @return {boolean} True si le mode maintenance est activé.
 */
function isMaintenanceMode() {
  try {
    const maintenanceMode = PropertiesService.getScriptProperties().getProperty('MAINTENANCE_MODE');
    return maintenanceMode === 'true';
  } catch (error) {
    console.error('Erreur lors de la vérification du mode maintenance:', error);
    return false; // Par défaut, ne pas bloquer l'accès en cas d'erreur.
  }
}

/**
 * Récupère la configuration Firebase de manière sécurisée.
 * @return {Object} Configuration Firebase.
 */
function getFirebaseConfig() {
  // ... (votre fonction getFirebaseConfig existante et bien conçue peut rester ici)
  // Assurez-vous qu'elle est complète. Par souci de clarté, je ne la reproduis pas ici.
  // La version que vous avez fournie précédemment est excellente.
  try {
    const scriptProperties = PropertiesService.getScriptProperties();
    const config = {};
    const requiredKeys = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId'];
    const optionalKeys = ['databaseURL', 'functionsRegion'];
    const allKeys = [...requiredKeys, ...optionalKeys];

    allKeys.forEach(key => {
      const value = scriptProperties.getProperty(key);
      if (value) {
        config[key] = value;
      }
    });

    if (requiredKeys.some(key => !config[key])) {
      throw new Error(`Configuration Firebase incomplète. Clés manquantes: ${requiredKeys.filter(key => !config[key]).join(', ')}`);
    }
    
    // Vous pouvez aussi ajouter la logique pour les émulateurs ici si besoin.
    
    return config;

  } catch (error) {
    console.error('Erreur critique lors de la récupération de la configuration Firebase:', error.message);
    return { error: error.message };
  }
}


/**
 * Crée une page d'erreur personnalisée.
 * @param {string} title - Titre de l'erreur.
 * @param {string} message - Message d'erreur.
 * @param {string} buttonText - Texte du bouton de retour.
 * @return {HtmlOutput} Page d'erreur formatée.
 */
function createErrorPage(title, message, buttonText) {
  // ... (votre fonction createErrorPage existante est parfaite et peut rester ici)
  // Je ne la reproduis pas pour garder la réponse ciblée sur doGet.
  const htmlContent = `
    <!DOCTYPE html>
    <html>
      <head>
        <base target="_top">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>SIGMA - Erreur</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
          body { display: flex; align-items: center; justify-content: center; min-height: 100vh; background-color: #f8f9fa; }
          .error-container { text-align: center; max-width: 500px; padding: 2rem; background: white; border-radius: 0.5rem; box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1); }
          .error-icon { font-size: 4rem; color: #dc3545; }
        </style>
      </head>
      <body>
        <div class="error-container">
          <div class="error-icon">⚠️</div>
          <h1 class="display-5 mt-3">${title}</h1>
          <p class="lead">${message}</p>
          <a href="<?= ScriptApp.getService().getUrl(); ?>" class="btn btn-primary mt-3">${buttonText}</a>
        </div>
      </body>
    </html>
  `;
  return HtmlService.createTemplate(htmlContent)
    .evaluate()
    .setTitle(`SIGMA - ${title}`)
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
}
