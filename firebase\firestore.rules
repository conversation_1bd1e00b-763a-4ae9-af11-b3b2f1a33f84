rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // --- Fonctions Utilitaires Générales ---
    function isAuthenticated() {
      return request.auth != null;
    }

    function isRole(role) {
      return isAuthenticated() && request.auth.token.role == role;
    }

    function hasRole(role) {
      return isRole(role) || isRole('admin');
    }

    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    // --- Fonctions de Validation pour la collection 'emprunts' ---
    function isValidEmpruntData(data) {
      return 'nom' in data && data.nom is string && data.nom.size() > 0 &&
             'lieu' in data && data.lieu is string && data.lieu.size() > 0 &&
             'dateDepart' in data && data.dateDepart is timestamp &&
             'dateRetour' in data && data.dateRetour is timestamp &&
             data.dateRetour > data.dateDepart &&
             'statut' in data && data.statut in ['Pas prêt', 'Prêt', 'Parti', 'Revenu', 'Inventorié'];
    }

    // =============================================================
    // --- RÈGLES PAR COLLECTION ---
    // =============================================================

    match /users/{userId} {
      allow get: if isOwner(userId) || isRole('admin');
      allow list: if isRole('admin');

      // La création/suppression est gérée par les Cloud Functions sécurisées.
      allow create, delete: if false;

      // Un utilisateur peut modifier son propre profil (champs limités), un admin peut tout faire.
      allow update: if isRole('admin') ||
                      (isOwner(userId) &&
                       resource != null &&
                       request.resource.data.diff(resource.data).affectedKeys().hasOnly(['displayName', 'phoneNumber']));
    }

    match /emprunts/{empruntId} {
      allow read: if isAuthenticated();

      // Tout utilisateur authentifié peut créer un emprunt si les données sont valides et le statut initial est correct.
      allow create: if isAuthenticated() &&
                      isValidEmpruntData(request.resource.data) &&
                      request.resource.data.statut == 'Pas prêt';

      // Un régisseur ou un admin peut mettre à jour un emprunt si les données restent valides
      // ET que le champ 'createdAt' n'est pas modifié.
      allow update: if hasRole('regisseur') &&
                      isValidEmpruntData(request.resource.data) &&
                      resource != null &&
                      'createdAt' in resource.data &&
                      'createdAt' in request.resource.data &&
                      request.resource.data.createdAt == resource.data.createdAt;

      // Un régisseur ou un admin peut supprimer un emprunt.
      allow delete: if hasRole('regisseur');
    }

    // --- Règles par défaut pour les autres collections (à affiner plus tard) ---
    match /stocks/{stockId} {
      allow read, write: if hasRole('regisseur');
    }
    match /modules/{moduleId} {
      allow read: if isAuthenticated();
      allow write: if hasRole('regisseur');
    }
    match /livraisons/{livraisonId} {
      allow read: if isAuthenticated();
      allow write: if hasRole('regisseur');
    }

    // Règle de sécurité par défaut : tout refuser.
    match /{path=**} {
        allow read, write: if false;
    }
  }
}