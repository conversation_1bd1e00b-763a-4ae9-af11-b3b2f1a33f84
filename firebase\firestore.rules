rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // --- Fonctions Utilitaires ---
    function isAuthenticated() {
      return request.auth != null;
    }

    function hasRole(role) {
      return isAuthenticated() && (request.auth.token.role == role || request.auth.token.role == 'admin');
    }

    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    // --- Fonctions de Validation de Données ---
    function isValidEmpruntData(data) {
      return 'nom' in data && data.nom is string && data.nom.size() > 0 &&
             'lieu' in data && data.lieu is string && data.lieu.size() > 0 &&
             'dateDepart' in data && data.dateDepart is timestamp &&
             'dateRetour' in data && data.dateRetour is timestamp &&
             data.dateRetour > data.dateDepart &&
             'statut' in data && data.statut in ['<PERSON><PERSON> prêt', '<PERSON>r<PERSON><PERSON>', 'Parti', 'Revenu', 'Inventorié'] &&
             // Validation optionnelle des champs supplémentaires s'ils sont présents
             (!('secteur' in data) || data.secteur is string) &&
             (!('emprunteur' in data) || data.emprunteur is string) &&
             (!('createdAt' in data) || data.createdAt is timestamp);
    }

    // --- RÈGLES PAR COLLECTION ---

    match /users/{userId} {
      allow get: if isOwner(userId) || hasRole('admin');
      allow list: if hasRole('admin');
      allow create: if false; // Géré par Cloud Function
      allow delete: if false; // Géré par Cloud Function

      // L'admin peut tout faire. L'utilisateur peut modifier SEULEMENT displayName et phoneNumber de son propre profil.
      allow update: if hasRole('admin') ||
                      (isOwner(userId) &&
                       resource != null &&
                       request.resource.data.keys().hasOnly(['displayName', 'phoneNumber']));
    }

    match /emprunts/{empruntId} {
      allow read: if isAuthenticated();

      // Création : Tout utilisateur authentifié si les données sont valides et le statut initial est correct.
      // La condition "resource == null" est implicite dans une règle "create".
      allow create: if isAuthenticated() &&
                      isValidEmpruntData(request.resource.data) &&
                      request.resource.data.statut == 'Pas prêt';

      // Mise à jour : Régisseur/Admin si les données restent valides ET que createdAt n'est pas modifié.
      allow update: if hasRole('regisseur') &&
                      isValidEmpruntData(request.resource.data) &&
                      resource != null &&
                      'createdAt' in resource.data &&
                      'createdAt' in request.resource.data &&
                      request.resource.data.createdAt == resource.data.createdAt;

      // Suppression : Régisseur/Admin.
      allow delete: if hasRole('regisseur');
    }

    // --- Règles par défaut pour les autres collections ---
    match /stocks/{stockId} { allow read, write: if hasRole('regisseur'); }
    match /modules/{moduleId} {
      allow read: if isAuthenticated();
      allow write: if hasRole('regisseur');
    }
    match /livraisons/{livraisonId} {
      allow read: if isAuthenticated();
      allow write: if hasRole('regisseur');
    }

    // Règle de sécurité par défaut : tout refuser.
    match /{path=**} {
      allow read, write: if false;
    }
  }
}