#!/usr/bin/env bash
#
# sigma_task_runner.sh (v2.1 - Orchestrateur d'Agent IA)
# ------------------------------------------------------------
# Utilise un framework de prompts pour exécuter une tâche SIGMA
# de manière semi-automatisée.
# ------------------------------------------------------------
set -euo pipefail
set -x # Active le mode debug pour voir chaque commande

# --- Configuration ---
PROMPT_TEMPLATES_FILE="prompt_templates/templates.yaml"
TASKS_FILE="SIGMA_tasks.yaml" # Défini ici pour plus de clarté

# --- Validation des arguments et dépendances ---
if [[ $# -lt 1 ]]; then
  echo "Usage: $0 TASK_ID" >&2
  exit 1
fi
TASK_ID="$1"

# Vérification des dépendances (yq, jq)
if ! command -v yq &> /dev/null || ! command -v jq &> /dev/null; then
    echo "Erreur : 'yq' et 'jq' sont des dépendances requises." >&2
    exit 1
fi

# --- Extraction et affichage de la tâche ---
task_json=$(yq -o=json ".[] | select(.task_id==\"$TASK_ID\")" "$TASKS_FILE" || true)
if [[ -z "$task_json" ]]; then
  echo "Tâche $TASK_ID non trouvée dans $TASKS_FILE" >&2
  exit 1
fi

kind=$(echo "$task_json" | jq -r '.kind')
desc=$(echo "$task_json" | jq -r '.description')
status=$(echo "$task_json" | jq -r '.status')

echo "------------------------------------------------------------"
echo "Tâche       : $TASK_ID"
echo "Description : $desc"
echo "Type        : $kind"
echo "Statut      : $status"
echo "------------------------------------------------------------"

if [[ "$status" == "done" ]]; then
  echo "La tâche est déjà marquée comme terminée. Rien à faire."
  exit 0
fi

# --- Préparation de la branche Git ---
branch="auto/${TASK_ID,,}" # Convertit le nom de la tâche en minuscules
if git show-ref --verify --quiet "refs/heads/$branch"; then
  echo "La branche '$branch' existe déjà, on bascule dessus."
  git checkout "$branch"
else
  echo "Création de la nouvelle branche '$branch'..."
  git checkout -b "$branch"
fi


# ------------------------------------------------------------------
# 1. Génération du Prompt et Passe-Main (Version Finale)
# ------------------------------------------------------------------
echo "Génération du prompt pour la tâche de type '$kind'..."

# Sélection du template system/user basé sur le 'kind'
SYSTEM_PROMPT=$(yq -r ".$kind.system" "$PROMPT_TEMPLATES_FILE")
USER_TEMPLATE=$(yq -r ".$kind.user" "$PROMPT_TEMPLATES_FILE")

if [[ "$SYSTEM_PROMPT" == "null" || "$USER_TEMPLATE" == "null" ]]; then
  echo "Erreur : Aucun modèle de prompt trouvé pour le type '$kind' dans $PROMPT_TEMPLATES_FILE" >&2
  exit 1
fi

# --- CORRECTION : Extraction de TOUS les placeholders depuis le JSON de la tâche ---
files=$(echo "$task_json" | jq -r '.target_files | join(" ")') # Récupère la liste des fichiers

# Récupère la commande de test et échappe les caractères spéciaux pour sed (&, /, \)
acceptance_cmd=$(echo "$task_json"| jq -r '.acceptance_test_cmd' | sed -e 's/[&/\\]/\\&/g') 

# --- CORRECTION : Remplacement de TOUS les placeholders ---
USER_FINAL=$(echo "$USER_TEMPLATE" | \
  sed "s/{{TASK_ID}}/$TASK_ID/g" | \
  sed "s/{{DESCRIPTION}}/$desc/g" | \
  sed "s|{{FILES}}|$files|g" | \
  sed "s|{{ACCEPTANCE_TEST_CMD}}|$acceptance_cmd|g" \
)

# Écriture du prompt complet dans le fichier temporaire
{
  echo "$SYSTEM_PROMPT"
  echo
  echo "$USER_FINAL"
} > prompt.txt

echo "✅ Fichier prompt.txt généré."



# Copie du prompt dans le presse-papiers
cat prompt.txt | clip
echo "✅ Prompt copié dans le presse-papiers."

# Instructions pour l'utilisateur
echo "------------------------------------------------------------"
echo "👉 ACTION REQUISE : "
echo "   1. Allez dans votre éditeur de code (VS Code)."
echo "   2. Ouvrez le panneau de l'Agent Augment."
echo "   3. Collez (Ctrl+V) le prompt et lancez l'Agent."
echo "------------------------------------------------------------"

# Pause en attendant l'action manuelle
read -p "Appuyez sur [Entrée] une fois que l'Agent a terminé son travail..."

# ------------------------------------------------------------------
# 2. Exécution des tests et commit
# ------------------------------------------------------------------
echo "L'Agent a terminé. Lancement des tests..."
Votre logique de test ici, par exemple :
if npm run test; then
  echo "✅ Tests passés avec succès."
  git add .
 git commit -m "feat($TASK_ID): $desc"
  git push
else
 echo "❌ Des tests ont échoué ! Veuillez corriger manuellement." >&2
 exit 1
fi

echo "------------------------------------------------------------"
echo "Processus terminé pour $TASK_ID. Créez une Pull Request."
echo "------------------------------------------------------------"