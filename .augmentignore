# Fichier .augmentignore pour le projet SIGMA

# Ignorer les dépendances et les builds locaux qui ne sont pas pertinents pour le contexte du code
/node_modules/
/dist/
/build/

# Ignorer les configurations locales de l'IDE qui n'apportent pas de contexte
.idea/
.vscode/

# Ignorer les fichiers système
.DS_Store

# EXEMPLE IMPORTANT : Si vous décidez que le contexte des dépendances est utile
# (par exemple, pour que l'agent connaisse les versions des packages Firebase),
# vous pouvez forcer leur inclusion même si elles sont dans le .gitignore.
# Pour cela, décommentez la ligne suivante :
# !/node_modules/

!.augment-guidelines