/**
 * Tests unitaires spécifiques pour createEmpruntCF
 */

const functions = require('firebase-functions/v1');

// Mock Firebase Admin SDK
const mockFirestore = {
  runTransaction: jest.fn(),
  collection: jest.fn(() => ({
    doc: jest.fn(() => ({
      id: 'test-emprunt-id',
      collection: jest.fn(() => ({
        doc: jest.fn()
      }))
    }))
  }))
};

const mockTimestamp = {
  now: jest.fn(() => ({ seconds: 1640995200, nanoseconds: 0 })),
  fromDate: jest.fn((date) => ({ seconds: Math.floor(date.getTime() / 1000), nanoseconds: 0 }))
};

const mockAdmin = {
  firestore: jest.fn(() => mockFirestore)
};

// Ajouter les propriétés Timestamp à mockAdmin.firestore
mockAdmin.firestore.Timestamp = mockTimestamp;

jest.mock('firebase-admin', () => mockAdmin);

// Import des fonctions à tester
const empruntsLogic = require('./emprunts');
const { z } = require('zod');

describe('createEmpruntCF Tests', () => {
  // Données valides pour les tests avec Zod
  const validEmpruntCFData = {
    nom: 'Test Emprunt CF',
    lieu: 'Test Lieu CF',
    dateDepart: { seconds: 1704844800, nanoseconds: 0 }, // Mock Timestamp
    dateRetour: { seconds: 1705276800, nanoseconds: 0 }, // Mock Timestamp
    secteur: 'PAF',
    emprunteur: 'test_user_id',
    referent: 'Test Referent',
    notes: 'Notes de test CF'
  };

  const adminContext = {
    auth: {
      uid: 'admin_uid',
      token: {
        role: 'admin'
      }
    }
  };

  const regisseurContext = {
    auth: {
      uid: 'regisseur_uid',
      token: {
        role: 'regisseur'
      }
    }
  };

  const userContext = {
    auth: {
      uid: 'user_uid',
      token: {
        role: 'utilisateur'
      }
    }
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('devrait créer un emprunt avec des données valides (admin)', async () => {
    // Mock la transaction Firestore pour retourner un succès
    mockFirestore.runTransaction.mockResolvedValue({
      id: 'test-emprunt-cf-id',
      ...validEmpruntCFData,
      statut: 'draft',
      createdBy: adminContext.auth.uid
    });

    const result = await empruntsLogic.createEmpruntCF(validEmpruntCFData, adminContext);

    expect(result.success).toBe(true);
    expect(result.emprunt).toBeDefined();
    expect(result.emprunt.id).toBe('test-emprunt-cf-id');
    expect(result.emprunt.nom).toBe(validEmpruntCFData.nom);
    expect(result.emprunt.statut).toBe('draft');
    expect(result.emprunt.createdBy).toBe(adminContext.auth.uid);
  });

  it('devrait créer un emprunt avec des données valides (regisseur)', async () => {
    // Mock la transaction Firestore pour retourner un succès
    mockFirestore.runTransaction.mockResolvedValue({
      id: 'test-emprunt-cf-id-2',
      ...validEmpruntCFData,
      statut: 'draft',
      createdBy: regisseurContext.auth.uid
    });

    const result = await empruntsLogic.createEmpruntCF(validEmpruntCFData, regisseurContext);

    expect(result.success).toBe(true);
    expect(result.emprunt.createdBy).toBe(regisseurContext.auth.uid);
  });

  it('devrait rejeter un utilisateur non authentifié', async () => {
    const unauthenticatedContext = {};

    await expect(
      empruntsLogic.createEmpruntCF(validEmpruntCFData, unauthenticatedContext)
    ).rejects.toThrow('Vous devez être connecté pour créer un emprunt');
  });

  it('devrait rejeter un utilisateur non autorisé', async () => {
    await expect(
      empruntsLogic.createEmpruntCF(validEmpruntCFData, userContext)
    ).rejects.toThrow('Seuls les administrateurs et régisseurs peuvent créer des emprunts');
  });

  it('devrait rejeter des données invalides avec Zod', async () => {
    const invalidData = {
      nom: '', // Nom vide
      lieu: 'Test Lieu',
      dateDepart: { seconds: 1704844800, nanoseconds: 0 },
      dateRetour: { seconds: 1705276800, nanoseconds: 0 },
      secteur: 'PAF',
      emprunteur: 'test_user_id'
    };

    await expect(
      empruntsLogic.createEmpruntCF(invalidData, adminContext)
    ).rejects.toThrow('Données invalides');
  });

  it('devrait rejeter des champs obligatoires manquants', async () => {
    const incompleteData = { ...validEmpruntCFData };
    delete incompleteData.nom;

    await expect(
      empruntsLogic.createEmpruntCF(incompleteData, adminContext)
    ).rejects.toThrow('Données invalides');
  });

  it('devrait rejeter des dates invalides (retour avant départ)', async () => {
    const invalidDateData = {
      ...validEmpruntCFData,
      dateDepart: { seconds: 1705276800, nanoseconds: 0 }, // 15 janvier
      dateRetour: { seconds: 1704844800, nanoseconds: 0 }  // 10 janvier (avant départ)
    };

    await expect(
      empruntsLogic.createEmpruntCF(invalidDateData, adminContext)
    ).rejects.toThrow('La date de retour doit être postérieure à la date de départ');
  });

  it('devrait gérer les champs optionnels', async () => {
    const minimalData = {
      nom: 'Test Minimal CF',
      lieu: 'Test Lieu CF',
      dateDepart: { seconds: 1704844800, nanoseconds: 0 },
      dateRetour: { seconds: 1705276800, nanoseconds: 0 },
      secteur: 'PAF',
      emprunteur: 'test_user_id'
      // Pas de referent ni notes
    };

    // Mock la transaction Firestore pour retourner un succès
    mockFirestore.runTransaction.mockResolvedValue({
      id: 'test-emprunt-cf-minimal',
      ...minimalData,
      referent: '',
      notes: '',
      statut: 'draft',
      createdBy: adminContext.auth.uid
    });

    const result = await empruntsLogic.createEmpruntCF(minimalData, adminContext);

    expect(result.success).toBe(true);
    expect(result.emprunt.referent).toBe('');
    expect(result.emprunt.notes).toBe('');
  });

  it('devrait valider le schéma Zod correctement', () => {
    // Test du schéma Zod directement
    const validData = {
      nom: 'Test Schema',
      lieu: 'Test Lieu Schema',
      dateDepart: new Date('2025-01-10'),
      dateRetour: new Date('2025-01-15'),
      secteur: 'PAF',
      emprunteur: 'test_user_id'
    };

    expect(() => empruntsLogic.CreateEmpruntSchema.parse(validData)).not.toThrow();
  });

  it('devrait rejeter un schéma Zod invalide', () => {
    const invalidData = {
      nom: '', // Nom vide
      lieu: 'Test Lieu',
      dateDepart: new Date('2025-01-15'),
      dateRetour: new Date('2025-01-10'), // Date de retour avant départ
      secteur: 'PAF',
      emprunteur: 'test_user_id'
    };

    expect(() => empruntsLogic.CreateEmpruntSchema.parse(invalidData)).toThrow();
  });

  it('devrait gérer les erreurs de transaction Firestore', async () => {
    // Mock une erreur de transaction
    mockFirestore.runTransaction.mockRejectedValue(new Error('Transaction failed'));

    await expect(
      empruntsLogic.createEmpruntCF(validEmpruntCFData, adminContext)
    ).rejects.toThrow('Erreur interne lors de la création de l\'emprunt');
  });

  it('devrait gérer les dates au format Date', async () => {
    const dataWithDates = {
      ...validEmpruntCFData,
      dateDepart: new Date('2025-01-10'),
      dateRetour: new Date('2025-01-15')
    };

    mockFirestore.runTransaction.mockResolvedValue({
      id: 'test-emprunt-cf-dates',
      ...dataWithDates,
      statut: 'draft',
      createdBy: adminContext.auth.uid
    });

    const result = await empruntsLogic.createEmpruntCF(dataWithDates, adminContext);

    expect(result.success).toBe(true);
    expect(result.emprunt.id).toBe('test-emprunt-cf-dates');
  });
});
