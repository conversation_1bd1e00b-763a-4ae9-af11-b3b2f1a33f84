<!DOCTYPE html>
<html>
  <head>
    <base target="_top">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>SIGMA - Test de Connexion</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Styles personnalisés -->
    <style>
      body {
        padding: 20px;
        font-family: Arial, sans-serif;
      }
      .test-container {
        max-width: 800px;
        margin: 0 auto;
      }
      .test-section {
        margin-bottom: 30px;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 5px;
      }
      .test-button {
        margin-right: 10px;
        margin-bottom: 10px;
      }
      #testResults {
        margin-top: 20px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 5px;
        min-height: 100px;
      }
      .result-success {
        color: green;
      }
      .result-error {
        color: red;
      }
    </style>

    <!-- Scripts de base -->
    <?!= include('js_loggingUtils'); ?>
  </head>
  <body>
    <div class="test-container">
      <h1>Test de Navigation vers la Page de Connexion</h1>
      <p>Cette page permet de tester la navigation vers la page de connexion et d'identifier les problèmes potentiels.</p>

      <div class="test-section">
        <h2>Tests de Navigation</h2>
        <p>Cliquez sur les boutons ci-dessous pour tester différentes méthodes de navigation vers la page de connexion.</p>
        <div>
          <button id="testDirectLink" class="btn btn-primary test-button">Lien Direct (window.location)</button>
          <button id="testQueryParam" class="btn btn-info test-button">Paramètre URL (page=login_simple)</button>
          <button id="testLoadPage" class="btn btn-success test-button">Fonction loadPage()</button>
        </div>
      </div>

      <div class="test-section">
        <h2>Tests de Contenu</h2>
        <p>Vérifier si le contenu de la page de connexion est correctement chargé.</p>
        <div>
          <button id="testFetchContent" class="btn btn-warning test-button">Récupérer Contenu via AJAX</button>
        </div>
      </div>

      <h3>Résultats des Tests</h3>
      <div id="testResults">Les résultats des tests s'afficheront ici...</div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <script>
      // Fonction pour ajouter un résultat au conteneur de résultats
      function appendToResults(message, type = 'info') {
        const resultElement = document.getElementById('testResults');
        const messageElement = document.createElement('p');
        messageElement.className = type === 'error' ? 'result-error' : type === 'success' ? 'result-success' : '';
        messageElement.textContent = message;
        resultElement.appendChild(messageElement);
        
        // Log également le message
        if (typeof logInfo === 'function' && type === 'info') {
          logInfo(message);
        } else if (typeof logError === 'function' && type === 'error') {
          logError(message);
        }
        
        console.log(`[${type.toUpperCase()}] ${message}`);
      }

      // Initialiser les tests quand le document est prêt
      document.addEventListener('DOMContentLoaded', function() {
        appendToResults('Page de test chargée');
        
        // Test 1: Lien direct
        document.getElementById('testDirectLink').addEventListener('click', function() {
          appendToResults('Test de navigation directe vers login_simple.html...');
          setTimeout(() => {
            try {
              window.location.href = '<?= ScriptApp.getService().getUrl(); ?>?page=login_simple';
            } catch (error) {
              appendToResults(`Erreur lors de la navigation directe: ${error.message}`, 'error');
            }
          }, 1000); // Délai pour voir le message avant la redirection
        });
        
        // Test 2: Paramètre URL
        document.getElementById('testQueryParam').addEventListener('click', function() {
          appendToResults('Test de navigation avec paramètre URL...');
          setTimeout(() => {
            try {
              const baseUrl = '<?= ScriptApp.getService().getUrl(); ?>';
              const url = new URL(baseUrl);
              url.searchParams.set('page', 'login_simple');
              window.location.href = url.toString();
            } catch (error) {
              appendToResults(`Erreur lors de la navigation avec paramètre: ${error.message}`, 'error');
            }
          }, 1000);
        });
        
        // Test 3: Fonction loadPage
        document.getElementById('testLoadPage').addEventListener('click', function() {
          appendToResults('Test de navigation avec la fonction loadPage()...');
          setTimeout(() => {
            try {
              // Définir la fonction loadPage si elle n'existe pas
              if (typeof loadPage !== 'function') {
                window.loadPage = function(page) {
                  console.log('Navigation vers la page:', page);
                  window.location.href = '<?= ScriptApp.getService().getUrl(); ?>?page=' + page;
                };
              }
              
              loadPage('login_simple');
            } catch (error) {
              appendToResults(`Erreur lors de l'utilisation de loadPage: ${error.message}`, 'error');
            }
          }, 1000);
        });
        
        // Test 4: Récupérer le contenu via AJAX
        document.getElementById('testFetchContent').addEventListener('click', function() {
          appendToResults('Test de récupération du contenu via AJAX...');
          
          google.script.run
            .withSuccessHandler(function(content) {
              if (content && content.length > 0) {
                appendToResults(`Contenu récupéré avec succès (${content.length} caractères)`, 'success');
                
                // Vérifier si le contenu contient des éléments clés
                const hasDoctype = content.includes('<!DOCTYPE html>');
                const hasLoginForm = content.includes('login-form');
                const hasGoogleSignin = content.includes('google-signin');
                
                appendToResults(`- Contient DOCTYPE: ${hasDoctype ? 'Oui' : 'Non'}`, hasDoctype ? 'success' : 'error');
                appendToResults(`- Contient formulaire de connexion: ${hasLoginForm ? 'Oui' : 'Non'}`, hasLoginForm ? 'success' : 'error');
                appendToResults(`- Contient bouton Google: ${hasGoogleSignin ? 'Oui' : 'Non'}`, hasGoogleSignin ? 'success' : 'error');
              } else {
                appendToResults('Contenu vide ou invalide reçu', 'error');
              }
            })
            .withFailureHandler(function(error) {
              appendToResults(`Erreur lors de la récupération du contenu: ${error}`, 'error');
            })
            .getLoginPageContent();
        });
      });
    </script>
  </body>
</html>
