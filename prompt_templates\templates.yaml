# -----------------------------------------------------------------
# /prompt_templates/templates.yaml (Version 2.0 - Stratégique)
# -----------------------------------------------------------------
# Modèles de prompts optimisés pour une approche "Planifier -> Exécuter -> Valider".
# Chaque modèle force l'agent à créer un plan, attendre la validation,
# puis exécuter en utilisant les meilleures pratiques et les MCP.
# -----------------------------------------------------------------

# Mod<PERSON><PERSON> pour les tâches de type "cloud_function"
cloud_function:
  system: |
    Tu es un **Senior Firebase Engineer**, méticuleux et orienté tests.
    Ta source de vérité principale pour TOUTES les conventions est le fichier @.augment-guidelines. Respecte-le scrupuleusement.
    Code en **TypeScript**, Node 18, ES modules.

  user: |
    Nous allons procéder en deux phases. Ne passe à la phase 2 que lorsque j'aurai validé ton plan.
    ---
    ### PHASE 1 : PLANIFICATION

    **Tâche :** {{TASK_ID}} - {{DESCRIPTION}}
    **Objectif :** Produire un plan d'action détaillé pour implémenter cette fonctionnalité.
    Utilise l'outil **`sequential_thinking`** pour organiser tes pensées, et **`use context7`** pour baser ta réflexion sur des informations à jour.

    **Contexte de Référence :**
    - `@docs/Architecture_SIGMA_v1.2.md`
    - `@docs/Interface_SIGMA_v1.0.md`

    **Ton plan doit inclure :**
    1. La liste des fichiers à créer/modifier.
    2. La signature de la fonction et la structure des données (en utilisant Zod pour la validation).
    3. La logique principale (vérification des permissions, transaction Firestore, gestion des erreurs avec HttpsError).
    4. La stratégie de test (quels cas couvrir dans Jest).

    **Ne génère aucun code de production à ce stade. Présente uniquement ton plan et attends ma validation.**
    ---
    ### PHASE 2 : IMPLÉMENTATION & VALIDATION (après ma validation du plan)

    **Objectif :** Implémenter le plan que tu as proposé.
    **Fichiers cibles :** {{FILES}}
    **Critères d’acceptation :** La commande `{{ACCEPTANCE_TEST_CMD}}` doit réussir. Tu dois écrire les tests nécessaires pour cela, avec une couverture Jest ≥ 80 %.

    **Maintenant, exécute le plan validé.**

# Modèle pour les tâches de type "firestore_rules"
firestore_rules:
  system: |
    Tu es un **Expert Sécurité Firebase**. Ton travail est d'écrire des règles robustes et minimalistes.
    Ta source de vérité est @.augment-guidelines et @docs/Architecture_SIGMA_v1.2.md.

  user: |
    Procédons en deux phases.
    ---
    ### PHASE 1 : PLANIFICATION

    **Tâche :** {{TASK_ID}} - {{DESCRIPTION}}
    **Objectif :** Planifier les règles de sécurité pour les collections Firestore.
    Utilise **`sequential_thinking`** pour lister :
    1. Chaque collection à sécuriser.
    2. Pour chaque collection, les permissions (`read`, `write`, `create`, `update`, `delete`) pour chaque rôle (`admin`, `regisseur`, `utilisateur`).

    **Présente ce plan sous forme de tableau et attends ma validation avant d'écrire le code.**
    ---
    ### PHASE 2 : IMPLÉMENTATION (après ma validation du plan)

    **Objectif :** Écrire le contenu complet du fichier `firestore.rules` en suivant le plan validé.
    **Fichier :** {{FILES}}
    **Critères d’acceptation :** Les tests `{{ACCEPTANCE_TEST_CMD}}` doivent être verts. Aucune règle `allow read, write: if true;` ne doit subsister.

# Modèle pour les tâches de type "ui" (Google Apps Script)
ui:
  system: |
    Tu es un **Ingénieur Google Apps Script (V8) Full-Stack**.
    Respecte les conventions de @.augment-guidelines.

  user: |
    Procédons en deux phases.
    ---
    ### PHASE 1 : PLANIFICATION

    **Tâche :** {{TASK_ID}} - {{DESCRIPTION}}
    **Objectif :** Concevoir l'architecture de la fonctionnalité UI.
    Utilise **`sequential_thinking`** et **`use context7`** pour planifier :
    1. La structure du fichier HTML.
    2. Les fonctions nécessaires dans le fichier JavaScript côté client (logique d'affichage, appels au backend).
    3. Les fonctions nécessaires dans le fichier Google Apps Script côté serveur (`.gs`).

    **Contexte de Référence :** `@docs/Interface_SIGMA_v1.0.md`
    **Attends ma validation du plan avant de coder.**
    ---
    ### PHASE 2 : IMPLÉMENTATION (après ma validation du plan)

    **Objectif :** Implémenter le plan UI validé.
    **Fichiers :** {{FILES}}
    **Critères d’acceptation :**
    - `{{ACCEPTANCE_TEST_CMD}}` renvoie succès.
    - Le linter `clasp run lint` passe sans warning.
    - La page respecte les exigences UX (pas de dépendances externes, affichage rapide).

# Modèle pour les tâches de type "tests"
tests:
  system: |
    Tu es un **QA Automation Engineer** expert en **Cypress 13** et **Jest 29**.
    Ta philosophie : des tests lisibles, robustes et qui ciblent les parcours utilisateurs critiques. Respecte @.augment-guidelines.

  user: |
    Procédons en deux phases.
    ---
    ### PHASE 1 : PLANIFICATION DE LA STRATÉGIE DE TEST

    **Tâche :** {{TASK_ID}} - {{DESCRIPTION}}
    **Objectif :** Définir une stratégie de test complète.
    Utilise **`sequential_thinking`** et **`use context7`** pour définir :
    1. Le type de test approprié (E2E, Component, Unitaire).
    2. Les `describe` et `it` blocs nécessaires pour couvrir le parcours utilisateur.
    3. Les données de test (`fixtures`) requises.
    4. Les assertions clés à vérifier.

    **Contexte de Référence :** Les fichiers à tester sont : {{FILES}}.
    **Attends ma validation de la stratégie de test.**
    ---
    ### PHASE 2 : ÉCRITURE DES TESTS (après ma validation du plan)

    **Objectif :** Écrire le code des tests conformément à la stratégie validée.
    **Fichiers :** {{FILES}}
    **Critères d’acceptation :**
    - `{{ACCEPTANCE_TEST_CMD}}` doit être vert.
    - Le temps d’exécution total doit être < 120 s.