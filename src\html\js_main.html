<script>
/**
 * <PERSON>ript principal de SIGMA
 *
 * Ce fichier contient la logique d'initialisation et les fonctions
 * globales utilisées dans toute l'application
 */

// Vérifier que nous sommes bien dans l'environnement navigateur
// pour éviter les erreurs d'exécution côté serveur
if (typeof document !== 'undefined') {
  // Variables globales pour l'état de l'application
  let appInitialized = false;

  // Exécuter une fois que la page est chargée
  document.addEventListener('DOMContentLoaded', function() {
    // Logger le démarrage de l'application
    logInfo('Application SIGMA en cours de démarrage', {
      url: window.location.href,
      timestamp: new Date().toISOString()
    });

    // Gérer les erreurs non capturées pour les logger
    window.addEventListener('error', function(event) {
      logError('Erreur non capturée', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error ? event.error.stack : null
      });

      // Ne pas empêcher le comportement par défaut
      return false;
    });

    // Gérer les rejets de promesses non capturés
    window.addEventListener('unhandledrejection', function(event) {
      logError('Promesse rejetée non capturée', {
        reason: event.reason ? event.reason.toString() : 'Raison inconnue',
        stack: event.reason && event.reason.stack ? event.reason.stack : null
      });
    });

    // Initialiser Firebase
    firebaseUtils.initFirebase()
      .then(() => {
        // Vérifier l'authentification
        return checkAuth();
      })
      .then(() => {
        // Initialiser l'interface utilisateur
        return initUI();
      })
      .then(() => {
        appInitialized = true;
        logInfo('Application SIGMA initialisée avec succès');

        // Déclencher un événement pour signaler que l'application est prête
        document.dispatchEvent(new Event('sigma-ready'));
      })
      .catch(error => {
        logError('Erreur lors de l\'initialisation de l\'application', error);
        showErrorMessage('Une erreur est survenue lors du chargement de l\'application. Veuillez réessayer ou contacter l\'administrateur.');
      });
  });
}

/**
 * Vérifie si l'utilisateur est connecté
 * @return {Promise} - Promesse résolue quand la vérification est terminée
 */
async function checkAuth() {
  // Vérifier que nous sommes dans l'environnement navigateur
  if (typeof document === 'undefined') {
    return Promise.resolve(); // Résoudre immédiatement dans l'environnement serveur
  }

  return new Promise(async (resolve, reject) => {
    try {
      // Vérifier d'abord si nous avons un utilisateur de test dans le localStorage
      // ou si nous sommes en mode test (paramètre URL test_auth=1 ou flag dans sessionStorage)
      const urlParams = new URLSearchParams(window.location.search);
      const isTestAuth = urlParams.get('test_auth') === '1' || sessionStorage.getItem('SIGMA_AUTH_STATUS') === 'authenticated';
      const urlRole = urlParams.get('role'); // Récupérer le rôle depuis l'URL si présent
      const sessionRole = sessionStorage.getItem('SIGMA_AUTH_ROLE'); // Récupérer le rôle depuis sessionStorage
      let testUser = null;

      try {
        const storedTestUser = localStorage.getItem('SIGMA_TEST_USER');
        if (storedTestUser) {
          testUser = JSON.parse(storedTestUser);
          logInfo('Utilisateur de test trouvé dans localStorage', { email: testUser.email });
        }
      } catch (storageError) {
        logWarning('Erreur lors de la récupération de l\'utilisateur de test', storageError);
      }

      // Si nous avons un utilisateur de test ou sommes en mode test
      if (testUser || isTestAuth) {
        logInfo('Mode authentification de test activé');

        // Créer un utilisateur de test par défaut si nécessaire
        if (!testUser) {
          // Déterminer le rôle à utiliser (priorité : URL, puis sessionStorage, puis défaut)
          const roleToUse = urlRole || sessionRole || 'admin';

          testUser = {
            uid: 'test-user-' + Date.now(),
            email: roleToUse === 'admin' ? '<EMAIL>' :
                  (roleToUse === 'regisseur' ? '<EMAIL>' : '<EMAIL>'),
            displayName: roleToUse === 'admin' ? 'Administrateur Test' :
                        (roleToUse === 'regisseur' ? 'Régisseur Test' : 'Utilisateur Test'),
            photoURL: null,
            role: roleToUse
          };
          logInfo('Utilisateur de test créé', { email: testUser.email, role: roleToUse });

          // Stocker l'utilisateur dans localStorage pour les futures visites
          try {
            localStorage.setItem('SIGMA_TEST_USER', JSON.stringify(testUser));
          } catch (e) {
            logWarning('Impossible de stocker l\'utilisateur dans localStorage', e);
          }
        }

        // Déterminer le rôle à utiliser
        // Priorité : 1. Rôle dans l'URL, 2. Rôle dans sessionStorage, 3. Rôle dans l'utilisateur test, 4. Rôle par défaut (admin)
        const userRole = urlRole || sessionRole || (testUser.role || 'admin');

        // Mettre à jour l'état de l'application
        updateState({
          user: testUser,
          userRole: userRole,
          isAuthenticated: true
        });

        // Log pour débogage
        console.log('Authentification de test activée:', {
          user: testUser.displayName,
          role: userRole,
          isAuthenticated: true
        });

        // Mettre à jour sessionStorage pour maintenir la session
        sessionStorage.setItem('SIGMA_AUTH_STATUS', 'authenticated');
        sessionStorage.setItem('SIGMA_AUTH_ROLE', userRole);

        logInfo('Authentification de test réussie', { role: userRole });
        resolve();
        return; // Sortir de la fonction, pas besoin d'initialiser Firebase
      }

      // Sinon, continuer avec l'authentification Firebase normale
      // Utiliser directement firebaseUtils.getFirebaseAuth() au lieu de getAuthInstance()
      let auth;
      try {
        auth = await firebaseUtils.getFirebaseAuth();

        if (!auth) {
          // Firebase pas encore prêt, résoudre quand même pour ne pas bloquer l'UI
          logWarning('Auth non initialisée lors de checkAuth');
          resolve();
          return;
        }
      } catch (authError) {
        logError('Erreur lors de la récupération de l\'instance Auth', authError);
        resolve(); // Résoudre quand même pour ne pas bloquer l'UI
        return;
      }

      // Vérifier l'état de l'authentification
      auth.onAuthStateChanged(user => {
        if (user) {
          // Utilisateur connecté
          logInfo('Utilisateur connecté', {
            uid: user.uid,
            email: user.email,
            displayName: user.displayName
          });

          updateState({
            user: {
              uid: user.uid,
              email: user.email,
              displayName: user.displayName,
              photoURL: user.photoURL
            },
            isAuthenticated: true
          });

          // Vérifier les rôles de l'utilisateur (si la fonction existe)
          if (typeof getUserRole === 'function') {
            getUserRole(user).catch(error => {
              logError('Erreur lors de la récupération du rôle', error);
            });
          }
        } else {
          // Utilisateur non connecté
          logInfo('Aucun utilisateur connecté');
          updateState({ user: null, userRole: null, isAuthenticated: false });

          // Si l'authentification est requise, rediriger vers la page de connexion
          // Vérifier si nous sommes sur une page protégée
          const currentPath = window.location.search;
          if (currentPath.includes('page=admin_') || currentPath.includes('page=emprunts') ||
              currentPath.includes('page=stocks') || currentPath.includes('page=modules') ||
              currentPath.includes('page=livraisons')) {
            window.location.href = '?page=login';
          }
        }

        resolve();
      }, error => {
        logError('Erreur d\'authentification', error);
        reject(error);
      });
    } catch (error) {
      logError('Erreur lors de la vérification de l\'authentification', error);
      reject(error);
    }
  });
}

/**
 * Initialise l'interface utilisateur
 * @return {Promise} - Promesse résolue quand l'UI est initialisée
 */
function initUI() {
  // Vérifier que nous sommes dans l'environnement navigateur
  if (typeof document === 'undefined') {
    return Promise.resolve(); // Résoudre immédiatement dans l'environnement serveur
  }

  return new Promise((resolve, reject) => {
    try {
      logInfo('Initialisation de l\'interface utilisateur');

      // Initialiser les composants de l'interface
      const components = [
        'alertesStock',
        // Ajouter d'autres composants ici
      ];

      // Initialiser chaque composant
      Promise.all(components.map(initComponent))
        .then(() => {
          logInfo('Tous les composants UI ont été initialisés');
          resolve();
        })
        .catch(error => {
          logError('Erreur lors de l\'initialisation des composants UI', error);
          reject(error);
        });
    } catch (error) {
      logError('Erreur lors de l\'initialisation de l\'UI', error);
      reject(error);
    }
  });
}

/**
 * Initialise un composant UI spécifique
 * @param {string} componentId - ID du composant à initialiser
 * @return {Promise} - Promesse résolue quand le composant est initialisé
 */
function initComponent(componentId) {
  // Vérifier que nous sommes dans l'environnement navigateur
  if (typeof document === 'undefined') {
    return Promise.resolve(); // Résoudre immédiatement dans l'environnement serveur
  }

  return new Promise((resolve) => {
    // Placeholder pour l'initialisation d'un composant spécifique
    // À implémenter selon les besoins de chaque composant
    logInfo(`Initialisation du composant: ${componentId}`);

    // Pour l'instant, simplement résoudre la promesse
    // Plus tard, on ajoutera la logique spécifique à chaque composant
    setTimeout(resolve, 100); // Simule une initialisation asynchrone
  });
}

/**
 * Affiche un message d'erreur dans l'interface
 * @param {string} message - Message d'erreur à afficher
 */
function showErrorMessage(message) {
  // Vérifier que nous sommes dans l'environnement navigateur
  if (typeof document === 'undefined') return;

  // Vérifier si un conteneur de notification existe déjà
  let notificationContainer = document.getElementById('notifications');

  // Si non, en créer un
  if (!notificationContainer) {
    notificationContainer = document.createElement('div');
    notificationContainer.id = 'notifications';
    notificationContainer.style.position = 'fixed';
    notificationContainer.style.top = '20px';
    notificationContainer.style.right = '20px';
    notificationContainer.style.zIndex = '1000';
    document.body.appendChild(notificationContainer);
  }

  // Créer la notification
  const notification = document.createElement('div');
  notification.className = 'notification notification-error';
  notification.textContent = message;

  // Ajouter un bouton de fermeture
  const closeButton = document.createElement('button');
  closeButton.className = 'notification-close';
  closeButton.innerHTML = '&times;';
  closeButton.onclick = function() {
    notification.remove();
  };
  notification.appendChild(closeButton);

  // Ajouter la notification au conteneur
  notificationContainer.appendChild(notification);

  // Supprimer après un délai
  setTimeout(() => {
    notification.classList.add('fade-out');
    setTimeout(() => notification.remove(), 500);
  }, 5000);
}
</script>
