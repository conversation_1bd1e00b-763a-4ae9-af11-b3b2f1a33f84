<!DOCTYPE html>
<html>
  <head>
    <base target="_top">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>SIGMA - Connexion Simplifiée</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">

    <!-- Styles personnalisés -->
    <style>
      :root {
        --primary-color: #3f51b5;
        --secondary-color: #f50057;
      }

      body {
        background-color: #f5f5f5;
        font-family: 'Roboto', Arial, sans-serif;
      }

      .login-page {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .login-container {
        max-width: 400px;
        width: 100%;
      }

      .login-header {
        text-align: center;
        margin-bottom: 2rem;
      }

      .login-header h1 {
        font-size: 2.5rem;
        margin-bottom: 0.5rem;
        color: var(--primary-color);
      }

      .login-form .card {
        border-radius: 8px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
      }

      .login-form .card-body {
        padding: 2rem;
      }
    </style>

    <!-- Firebase -->
    <script src="https://www.gstatic.com/firebasejs/9.21.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.21.0/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.21.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.21.0/firebase-functions-compat.js"></script>
  </head>
  <body class="login-page">
    <div class="container">
      <div class="login-container">
        <div class="login-header">
          <h1>SIGMA</h1>
          <p>Système Informatique de Gestion du Matériel</p>
        </div>

        <div class="login-form">
          <div class="card">
            <div class="card-body">
              <h2 class="card-title text-center mb-4">Connexion</h2>

              <div id="login-error" class="alert alert-danger d-none" role="alert">
                <!-- Messages d'erreur -->
              </div>

              <div class="d-grid gap-3">
                <button id="google-signin" class="btn btn-primary btn-lg">
                  <i class="bi bi-google me-2"></i> Se connecter avec Google
                </button>

                <div class="alert alert-warning mt-3">
                  <h5>Erreur de configuration Firebase</h5>
                  <p>Le domaine de cette application n'est pas autorisé pour l'authentification Firebase.</p>
                  <hr>
                  <p class="mb-0">Pour contourner ce problème temporairement, utilisez le bouton ci-dessous :</p>
                </div>

                <button id="bypass-signin" class="btn btn-warning">
                  <i class="bi bi-shield-lock me-2"></i> Connexion de test (sans Firebase)
                </button>

                <div class="text-center">
                  <p class="text-muted">Connexion réservée aux utilisateurs autorisés</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Inclure les scripts utilitaires -->
    <?!= include('js_loggingUtils'); ?>

    <script>
      // Initialisation de Firebase
      let firebaseApp = null;
      let firebaseAuth = null;
      let isInitialized = false;

      // Fonction pour initialiser Firebase
      function initFirebase() {
        console.log('Initialisation de Firebase...');
        logInfo('Initialisation de Firebase depuis login_simple.html');

        return new Promise((resolve, reject) => {
          // Récupérer la configuration Firebase depuis le serveur
          google.script.run
            .withSuccessHandler(config => {
              try {
                if (!config || !config.apiKey) {
                  console.error('Configuration Firebase invalide:', config);
                  logError('Configuration Firebase invalide', config);
                  reject(new Error('Configuration Firebase invalide'));
                  return;
                }

                console.log('Configuration Firebase reçue');
                logInfo('Configuration Firebase reçue');

                // Initialiser Firebase
                if (!firebase.apps.length) {
                  firebaseApp = firebase.initializeApp(config);
                } else {
                  firebaseApp = firebase.app();
                }

                firebaseAuth = firebase.auth();
                isInitialized = true;

                console.log('Firebase initialisé avec succès');
                logInfo('Firebase initialisé avec succès');
                resolve();
              } catch (error) {
                console.error('Erreur lors de l\'initialisation de Firebase:', error);
                logError('Erreur lors de l\'initialisation de Firebase', error);
                reject(error);
              }
            })
            .withFailureHandler(error => {
              console.error('Erreur lors de la récupération de la configuration Firebase:', error);
              logError('Erreur lors de la récupération de la configuration Firebase', error);
              reject(error);
            })
            .getFirebaseConfig();
        });
      }

      // Fonction pour se connecter avec Google
      function signInWithGoogle() {
        console.log('Tentative de connexion avec Google...');
        logInfo('Tentative de connexion avec Google');

        return new Promise((resolve, reject) => {
          if (!isInitialized || !firebaseAuth) {
            const errMsg = 'Firebase n\'est pas initialisé';
            logError(errMsg);
            reject(new Error(errMsg));
            return;
          }

          const provider = new firebase.auth.GoogleAuthProvider();

          firebaseAuth.signInWithPopup(provider)
            .then(result => {
              console.log('Connexion réussie avec Google');
              logInfo('Connexion réussie avec Google');
              resolve(result);
            })
            .catch(error => {
              console.error('Erreur lors de la connexion avec Google:', error);
              logError('Erreur lors de la connexion avec Google', error);
              reject(error);
            });
        });
      }

      // Initialiser l'application au chargement de la page
      document.addEventListener('DOMContentLoaded', function() {
        console.log('Page de connexion chargée');
        logInfo('Page login_simple.html chargée');

        // Ajouter l'événement de clic sur le bouton de connexion Google
        document.getElementById('google-signin').addEventListener('click', function() {
          console.log('Clic sur le bouton de connexion Google');
          logInfo('Clic sur le bouton de connexion Google');

          // Initialiser Firebase si ce n'est pas déjà fait
          initFirebase()
            .then(() => {
              // Vérifier si l'utilisateur est déjà connecté
              if (firebaseAuth.currentUser) {
                console.log('Utilisateur déjà connecté, redirection vers le dashboard');
                logInfo('Utilisateur déjà connecté, redirection vers le dashboard');
                window.location.href = '<?= ScriptApp.getService().getUrl(); ?>';
                return;
              }

              // Se connecter avec Google
              return signInWithGoogle();
            })
            .then(result => {
              if (result) {
                console.log('Connexion réussie, redirection vers le dashboard');
                logInfo('Connexion réussie, redirection vers le dashboard');
                window.location.href = '<?= ScriptApp.getService().getUrl(); ?>';
              }
            })
            .catch(error => {
              console.error('Erreur:', error);
              logError('Erreur lors de la connexion', error);
              const errorElement = document.getElementById('login-error');
              errorElement.textContent = error.message || 'Erreur de connexion';
              errorElement.classList.remove('d-none');
            });
        });

        // Ajouter l'événement de clic sur le bouton de connexion de test (sans Firebase)
        document.getElementById('bypass-signin').addEventListener('click', function() {
          console.log('Clic sur le bouton de connexion de test');
          logInfo('Clic sur le bouton de connexion de test (sans Firebase)');

          try {
            // Simuler une connexion réussie
            const mockUser = {
              uid: 'test-user-123',
              email: '<EMAIL>',
              displayName: 'Utilisateur Test',
              photoURL: null
            };

            // Stocker l'utilisateur dans le localStorage pour simuler une session
            try {
              localStorage.setItem('SIGMA_TEST_USER', JSON.stringify(mockUser));
              logInfo('Utilisateur de test stocké dans localStorage');
            } catch (storageError) {
              logWarning('Impossible de stocker l\'utilisateur dans localStorage', storageError);
            }

            // Afficher un message de succès
            const errorElement = document.getElementById('login-error');
            errorElement.textContent = 'Connexion de test réussie! Redirection en cours...';
            errorElement.classList.remove('d-none');
            errorElement.classList.remove('alert-danger');
            errorElement.classList.add('alert-success');

            // Rediriger vers le dashboard avec un délai pour éviter les problèmes d'autorisation
            console.log('Connexion de test réussie, redirection vers le dashboard dans 2 secondes');
            logInfo('Connexion de test réussie, redirection vers le dashboard avec délai');

            // Utiliser une redirection directe avec un délai
            setTimeout(function() {
              // Construire l'URL de base sans paramètres
              const baseUrl = '<?= ScriptApp.getService().getUrl(); ?>'.split('?')[0];
              // Ajouter le paramètre test_auth
              window.location.href = baseUrl + '?test_auth=1';
            }, 2000); // Délai de 2 secondes
          } catch (error) {
            console.error('Erreur lors de la connexion de test:', error);
            logError('Erreur lors de la connexion de test', error);
            const errorElement = document.getElementById('login-error');
            errorElement.textContent = error.message || 'Erreur lors de la connexion de test';
            errorElement.classList.remove('d-none');
            errorElement.classList.add('alert-danger');
          }
        });

        // Initialiser Firebase automatiquement au chargement de la page
        initFirebase().catch(error => {
          console.error('Erreur d\'initialisation automatique:', error);
          logError('Erreur d\'initialisation automatique de Firebase', error);
        });
      });
    </script>
  </body>
</html>
