<script>
/**
 * Fonctions d'authentification pour SIGMA
 *
 * Ce fichier gère:
 * - La connexion et déconnexion
 * - La gestion des utilisateurs
 * - La vérification des rôles
 */

// Observer l'état d'authentification
async function initAuth() {
  // Vérifier que nous sommes dans l'environnement navigateur
  if (typeof document === 'undefined') return;

  try {
    // Vérifier d'abord si nous avons un utilisateur de test dans le localStorage
    // ou si nous sommes en mode test (paramètre URL test_auth=1 ou flag dans sessionStorage)
    const urlParams = new URLSearchParams(window.location.search);
    const isTestAuth = urlParams.get('test_auth') === '1' || sessionStorage.getItem('SIGMA_AUTH_STATUS') === 'authenticated';
    const urlRole = urlParams.get('role'); // Récupérer le rôle depuis l'URL si présent
    const sessionRole = sessionStorage.getItem('SIGMA_AUTH_ROLE'); // Récupérer le rôle depuis sessionStorage
    let testUser = null;

    try {
      const storedTestUser = localStorage.getItem('SIGMA_TEST_USER');
      if (storedTestUser) {
        testUser = JSON.parse(storedTestUser);
        logInfo('Utilisateur de test trouvé dans localStorage', { email: testUser.email });
      }
    } catch (storageError) {
      logWarning('Erreur lors de la récupération de l\'utilisateur de test', storageError);
    }

    // Si nous avons un utilisateur de test ou sommes en mode test
    if (testUser || isTestAuth) {
      logInfo('Mode authentification de test activé');

      // Créer un utilisateur de test par défaut si nécessaire
      if (!testUser) {
        // Déterminer le rôle à utiliser (priorité : URL, puis sessionStorage, puis défaut)
        const roleToUse = urlRole || sessionRole || 'admin';

        testUser = {
          uid: 'test-user-' + Date.now(),
          email: roleToUse === 'admin' ? '<EMAIL>' :
                (roleToUse === 'regisseur' ? '<EMAIL>' : '<EMAIL>'),
          displayName: roleToUse === 'admin' ? 'Administrateur Test' :
                      (roleToUse === 'regisseur' ? 'Régisseur Test' : 'Utilisateur Test'),
          photoURL: null,
          role: roleToUse
        };
        logInfo('Utilisateur de test créé', { email: testUser.email, role: roleToUse });

        // Stocker l'utilisateur dans localStorage pour les futures visites
        try {
          localStorage.setItem('SIGMA_TEST_USER', JSON.stringify(testUser));
        } catch (e) {
          logWarning('Impossible de stocker l\'utilisateur dans localStorage', e);
        }
      }

      // Déterminer le rôle à utiliser
      // Priorité : 1. Rôle dans l'URL, 2. Rôle dans sessionStorage, 3. Rôle dans l'utilisateur test, 4. Rôle par défaut (admin)
      const userRole = urlRole || sessionRole || (testUser.role || 'admin');

      // Mettre à jour l'état de l'application
      updateState({
        user: testUser,
        userRole: userRole,
        isAuthenticated: true
      });

      // Log pour débogage
      console.log('Authentification de test activée:', {
        user: testUser.displayName,
        role: userRole,
        isAuthenticated: true
      });

      // Mettre à jour sessionStorage pour maintenir la session
      sessionStorage.setItem('SIGMA_AUTH_STATUS', 'authenticated');
      sessionStorage.setItem('SIGMA_AUTH_ROLE', userRole);

      logInfo('Authentification de test réussie', { role: userRole });
      return; // Sortir de la fonction, pas besoin d'initialiser Firebase
    }

    // Sinon, continuer avec l'authentification Firebase normale
    const auth = await firebaseUtils.getFirebaseAuth();

    if (!auth) {
      logError("Instance Auth non disponible dans initAuth");
      return;
    }

    // Vérifier s'il y a un résultat de redirection à traiter
    try {
      await checkRedirectResult();
    } catch (redirectError) {
      logWarning('Erreur lors de la vérification du résultat de redirection', redirectError);
      // Continuer quand même avec l'authentification normale
    }

    auth.onAuthStateChanged(user => {
      if (user) {
        // Utilisateur connecté
        logInfo('Utilisateur connecté', {
          email: user.email,
          displayName: user.displayName
        });

        // Mettre à jour l'état de l'application
        updateState({ user: {
          uid: user.uid,
          email: user.email,
          displayName: user.displayName,
          photoURL: user.photoURL
        }});

        // Vérifier les rôles de l'utilisateur
        getUserRole(user);
      } else {
        // Utilisateur déconnecté
        logInfo('Utilisateur déconnecté');
        updateState({ user: null, userRole: null });

        // Rediriger vers la page de connexion si nécessaire
        // Vérifier si nous sommes sur une page protégée
        const currentPath = window.location.search;
        if (currentPath.includes('page=admin_') || currentPath.includes('page=emprunts') ||
            currentPath.includes('page=stocks') || currentPath.includes('page=modules') ||
            currentPath.includes('page=livraisons')) {
          // Utiliser directement l'URL de base sans paramètres
          const baseUrl = window.location.href.split('?')[0];
          window.location.href = baseUrl;
        }
      }
    });
  } catch (error) {
    logError('Erreur lors de l\'initialisation de l\'authentification', error);
  }
}

// Connecter un utilisateur avec Google
async function signInWithGoogle() {
  // Vérifier que nous sommes dans l'environnement navigateur
  if (typeof document === 'undefined') return Promise.reject("Environnement non-navigateur");

  try {
    // Ajouter des logs de débogage
    console.log('Tentative de connexion avec Google - Début de la fonction signInWithGoogle');

    // Vérifier si Firebase est initialisé
    if (typeof firebase === 'undefined' || !firebase.auth) {
      console.error('Firebase ou firebase.auth n\'est pas disponible');
      return Promise.reject("Firebase n'est pas correctement initialisé");
    }

    const auth = await firebaseUtils.getFirebaseAuth();
    console.log('Instance Auth récupérée:', !!auth);

    if (!auth) {
      console.error('Instance Auth non disponible');
      return Promise.reject("Instance Auth non disponible");
    }

    // Créer le provider Google
    console.log('Création du provider Google');
    const provider = new firebase.auth.GoogleAuthProvider();

    // Ajouter des scopes si nécessaire
    provider.addScope('email');
    provider.addScope('profile');

    console.log('Appel de signInWithPopup');

    // Vérifier si nous devons utiliser la redirection au lieu du popup
    // Vous pouvez ajouter un paramètre URL ou une préférence utilisateur pour basculer entre les deux méthodes
    const urlParams = new URLSearchParams(window.location.search);
    const useRedirect = urlParams.get('use_redirect') === '1';

    if (useRedirect) {
      console.log('Utilisation de signInWithRedirect au lieu de signInWithPopup');
      return auth.signInWithRedirect(provider)
        .then(() => {
          console.log('Redirection vers Google en cours...');
          // Cette promesse ne sera jamais résolue car la page sera redirigée
          return null;
        });
    } else {
      // Méthode par défaut avec popup
      return auth.signInWithPopup(provider)
      .then(result => {
        console.log('Connexion réussie avec Google');
        logInfo('Connexion réussie avec Google', {
          email: result.user.email,
          displayName: result.user.displayName
        });
        return result;
      })
      .catch(error => {
        logError('Erreur de connexion avec Google', error);

        // Conserver la notification existante pour la cohérence de l'UI
        showNotification('Erreur lors de la connexion: ' + error.message, 'error');

        // Afficher l'erreur dans l'élément #login-error si nous sommes sur la page de connexion
        const errorElement = document.getElementById('login-error');
        if (errorElement) {
          // Déterminer un message d'erreur adapté à l'utilisateur en fonction du code d'erreur
          let userMessage = 'Une erreur est survenue lors de la connexion. Veuillez réessayer.';

          // Personnaliser le message en fonction du code d'erreur
          if (error.code) {
            switch (error.code) {
              case 'auth/popup-closed-by-user':
                userMessage = 'La fenêtre de connexion a été fermée avant la fin du processus. Veuillez réessayer.';
                break;
              case 'auth/popup-blocked':
                userMessage = 'La fenêtre de connexion a été bloquée par votre navigateur. Veuillez autoriser les popups pour ce site et réessayer.';
                break;
              case 'auth/cancelled-popup-request':
                userMessage = 'La demande de connexion a été annulée. Veuillez réessayer.';
                break;
              case 'auth/network-request-failed':
                userMessage = 'Problème de connexion réseau. Veuillez vérifier votre connexion internet et réessayer.';
                break;
              case 'auth/user-disabled':
                userMessage = 'Ce compte utilisateur a été désactivé. Veuillez contacter l\'administrateur.';
                break;
              case 'auth/account-exists-with-different-credential':
                userMessage = 'Un compte existe déjà avec cette adresse email mais avec une méthode de connexion différente.';
                break;
              case 'auth/operation-not-allowed':
                userMessage = 'La connexion avec Google n\'est pas activée pour cette application. Veuillez contacter l\'administrateur.';
                break;
              case 'auth/timeout':
                userMessage = 'La connexion a expiré. Veuillez réessayer.';
                break;
              default:
                // Utiliser le message d'erreur de Firebase si disponible, sinon message générique
                userMessage = error.message || userMessage;
            }
          }

          // Mettre à jour le contenu de l'élément d'erreur
          errorElement.textContent = userMessage;

          // Rendre l'élément d'erreur visible
          errorElement.classList.remove('d-none');
        }

        throw error;
      });
  } catch (error) {
    logError('Erreur lors de l\'initialisation de l\'authentification pour la connexion', error);
    return Promise.reject(error);
  }
}

// Déconnecter l'utilisateur
async function signOut() {
  // Vérifier que nous sommes dans l'environnement navigateur
  if (typeof document === 'undefined') return Promise.reject("Environnement non-navigateur");

  try {
    // Obtenir l'URL du service pour la redirection
    let serviceUrl = '';
    const serviceUrlElement = document.getElementById('service-url');
    if (serviceUrlElement) {
      serviceUrl = serviceUrlElement.getAttribute('data-url');
      console.log('URL du service obtenue depuis l\'attribut data-url:', serviceUrl);
    } else {
      // Construire l'URL de base
      const currentUrl = window.location.href;
      const urlParts = currentUrl.split('/dev');
      if (urlParts.length > 1) {
        serviceUrl = urlParts[0] + '/dev';
        console.log('URL du service construite depuis l\'URL actuelle:', serviceUrl);
      } else {
        serviceUrl = currentUrl.split('?')[0];
        console.log('URL du service par défaut:', serviceUrl);
      }
    }

    // Créer une notification visuelle pour indiquer la déconnexion en cours
    const loadingMsg = document.createElement('div');
    loadingMsg.className = 'alert alert-info fixed-top m-3';
    loadingMsg.innerHTML = '<strong>Déconnexion en cours...</strong>';
    document.body.appendChild(loadingMsg);

    // Vérifier d'abord si nous avons un utilisateur de test ou une session
    let hasTestUser = false;
    let hasSession = false;

    try {
      // Vérifier localStorage
      const storedTestUser = localStorage.getItem('SIGMA_TEST_USER');
      if (storedTestUser) {
        // Supprimer l'utilisateur de test du localStorage
        localStorage.removeItem('SIGMA_TEST_USER');
        hasTestUser = true;
        logInfo('Utilisateur de test supprimé du localStorage');
      }

      // Vérifier sessionStorage
      if (sessionStorage.getItem('SIGMA_AUTH_STATUS') === 'authenticated') {
        sessionStorage.removeItem('SIGMA_AUTH_STATUS');
        sessionStorage.removeItem('SIGMA_AUTH_ROLE');
        hasSession = true;
        logInfo('Session d\'authentification supprimée');
      }
    } catch (storageError) {
      logWarning('Erreur lors de la suppression des données d\'authentification', storageError);
    }

    // Si nous avions un utilisateur de test ou une session, mettre à jour l'état et terminer
    if (hasTestUser || hasSession) {
      // Mettre à jour l'état de l'application
      updateState({ user: null, userRole: null, isAuthenticated: false });
      logInfo('Déconnexion de l\'utilisateur de test réussie');

      // Afficher une notification de succès
      loadingMsg.className = 'alert alert-success fixed-top m-3';
      loadingMsg.innerHTML = '<strong>Déconnexion réussie!</strong>';

      // Supprimer la notification après un court délai
      setTimeout(function() {
        loadingMsg.remove();
      }, 3000);

      return Promise.resolve();
    }

    // Sinon, continuer avec la déconnexion Firebase normale
    const auth = await firebaseUtils.getFirebaseAuth();

    if (!auth) {
      // Même en cas d'erreur, mettre à jour l'état local
      updateState({ user: null, userRole: null, isAuthenticated: false });

      // Afficher une notification d'erreur
      loadingMsg.className = 'alert alert-warning fixed-top m-3';
      loadingMsg.innerHTML = '<strong>Erreur Firebase!</strong> Déconnexion effectuée localement.';

      // Supprimer la notification après un court délai
      setTimeout(function() {
        loadingMsg.remove();
      }, 3000);

      return Promise.reject("Instance Auth non disponible");
    }

    return auth.signOut()
      .then(() => {
        logInfo('Déconnexion Firebase réussie');

        // Mettre à jour l'état local
        updateState({ user: null, userRole: null, isAuthenticated: false });

        // Mettre à jour la notification
        loadingMsg.className = 'alert alert-success fixed-top m-3';
        loadingMsg.innerHTML = '<strong>Déconnexion réussie!</strong>';

        // Supprimer la notification après un court délai
        setTimeout(function() {
          loadingMsg.remove();
        }, 3000);
      })
      .catch(error => {
        logError('Erreur de déconnexion Firebase', error);

        // Même en cas d'erreur, mettre à jour l'état local
        updateState({ user: null, userRole: null, isAuthenticated: false });

        // Mettre à jour la notification
        loadingMsg.className = 'alert alert-warning fixed-top m-3';
        loadingMsg.innerHTML = '<strong>Erreur lors de la déconnexion!</strong> Déconnexion effectuée localement.';

        // Supprimer la notification après un court délai
        setTimeout(function() {
          loadingMsg.remove();
        }, 3000);

        throw error;
      });
  } catch (error) {
    logError('Erreur lors de l\'initialisation de l\'authentification pour la déconnexion', error);

    // Afficher une notification d'erreur en dernier recours
    try {
      const errorMsg = document.createElement('div');
      errorMsg.className = 'alert alert-danger fixed-top m-3';
      errorMsg.innerHTML = '<strong>Erreur lors de la déconnexion!</strong> Veuillez rafraîchir la page.';
      document.body.appendChild(errorMsg);

      // Supprimer la notification après un court délai
      setTimeout(function() {
        errorMsg.remove();
      }, 5000);
    } catch (e) {
      console.error('Impossible d\'afficher la notification d\'erreur:', e);
    }

    return Promise.reject(error);
  }
}

// Utiliser directement les fonctions de firebaseUtils
// Les fonctions getFunctionsInstance, getFirestoreInstance et getAuthInstance ont été supprimées
// pour éviter les duplications et utiliser directement firebaseUtils

// Obtenir le rôle de l'utilisateur
async function getUserRole(user) {
  // Vérifier que nous sommes dans l'environnement navigateur
  if (typeof document === 'undefined') return Promise.resolve();

  if (!user) {
    logWarning('Tentative de récupération du rôle pour un utilisateur null');
    return;
  }

  try {
    // Vérifier d'abord si l'utilisateur a des custom claims
    // Force un rafraîchissement du token pour obtenir les dernières custom claims
    await user.getIdToken(true);
    const idTokenResult = await user.getIdTokenResult();

    if (idTokenResult.claims && idTokenResult.claims.role) {
      // Utiliser le rôle des custom claims
      const role = idTokenResult.claims.role;
      updateState({ userRole: role });
      logInfo('Rôle utilisateur récupéré depuis custom claims', { role });
      return;
    }

    // Si pas de custom claims, essayer de récupérer depuis Firestore (compatibilité)
    const db = await firebaseUtils.getFirestoreDb();

    if (!db) {
      logError("Instance Firestore non disponible dans getUserRole");
      return;
    }

    const userDoc = await db.collection('users').doc(user.uid).get();

    if (userDoc.exists) {
      const userData = userDoc.data();
      updateState({ userRole: userData.role });
      logInfo('Rôle utilisateur récupéré depuis Firestore', { role: userData.role });

      // Appeler la Cloud Function pour migrer ce rôle vers custom claims
      try {
        const functions = await firebaseUtils.getFirebaseFunctions();
        if (functions) {
          const getUserRoleFunction = functions.httpsCallable('getUserRole');
          await getUserRoleFunction({ uid: user.uid });
          logInfo('Migration du rôle vers custom claims demandée');
        }
      } catch (fnError) {
        logWarning('Erreur lors de la migration du rôle vers custom claims', fnError);
        // Continuer quand même, car le rôle Firestore a été récupéré
      }
    } else {
      // Si l'utilisateur n'existe pas dans Firestore, utiliser le rôle par défaut
      // La Cloud Function onUserCreated devrait déjà avoir défini les custom claims
      updateState({ userRole: 'utilisateur' });
      logInfo('Rôle par défaut utilisé', { role: 'utilisateur' });
    }
  } catch (error) {
    logError('Erreur lors de la récupération du rôle', error);
  }
}

// Vérifier si l'utilisateur a un rôle spécifique
function hasRole(requiredRole) {
  const { userRole } = appState;

  // Si l'utilisateur est admin, il a accès à tout
  if (userRole === 'admin') return true;

  // Sinon, vérifier le rôle spécifique
  if (requiredRole === 'regisseur') {
    return userRole === 'regisseur' || userRole === 'admin';
  }

  // Pour le rôle 'utilisateur', tous les utilisateurs connectés y ont accès
  if (requiredRole === 'utilisateur') {
    return !!userRole;
  }

  return false;
}

// Vérifier le résultat de la redirection après signInWithRedirect
async function checkRedirectResult() {
  try {
    console.log('Vérification du résultat de redirection');
    const auth = await firebaseUtils.getFirebaseAuth();

    if (!auth) {
      console.error('Instance Auth non disponible dans checkRedirectResult');
      return;
    }

    const result = await auth.getRedirectResult();

    if (result.user) {
      console.log('Connexion par redirection réussie');
      logInfo('Connexion réussie avec Google (redirection)', {
        email: result.user.email,
        displayName: result.user.displayName
      });

      // Mettre à jour l'interface utilisateur
      updateState({ user: {
        uid: result.user.uid,
        email: result.user.email,
        displayName: result.user.displayName,
        photoURL: result.user.photoURL
      }});

      // Vérifier les rôles de l'utilisateur
      getUserRole(result.user);
    }
  } catch (error) {
    console.error('Erreur lors de la vérification du résultat de redirection', error);
    logError('Erreur lors de la vérification du résultat de redirection', error);

    // Afficher l'erreur dans l'élément #login-error si nous sommes sur la page de connexion
    const errorElement = document.getElementById('login-error');
    if (errorElement) {
      errorElement.textContent = error.message || 'Erreur lors de la connexion avec Google';
      errorElement.classList.remove('d-none');
    }
  }
}
</script>
