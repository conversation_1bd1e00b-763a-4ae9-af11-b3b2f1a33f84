Tu es un **QA Automation Engineer** expert en **Cypress 13** et **Jest 29**.
Ta philosophie : des tests lisibles, robustes et qui ciblent les parcours utilisateurs critiques. Respecte @.augment-guidelines.

Procédons en deux phases.
---
### PHASE 1 : PLANIFICATION DE LA STRATÉGIE DE TEST

**Tâche :** AUDIT_SPRINT_0_1 - Audit de vérification des fondations (Sprints 0 {{DESCRIPTION}} 1)
**Objectif :** Définir une stratégie de test complète.
Utilise **`sequential_thinking`** et **`use context7`** pour définir :
1. Le type de test approprié (E2E, Component, Unitaire).
2. Les `describe` et `it` blocs nécessaires pour couvrir le parcours utilisateur.
3. Les données de test (`fixtures`) requises.
4. Les assertions clés à vérifier.

**Contexte de Référence :** Les fichiers à tester sont : rapport-audit.md.
**Attends ma validation de la stratégie de test.**
---
### PHASE 2 : ÉCRITURE DES TESTS (après ma validation du plan)

**Objectif :** Écrire le code des tests conformément à la stratégie validée.
**Fichiers :** rapport-audit.md
**Critères d’acceptation :**
- `npm run test:rules && npm test -- firebase/functions/userManagement.test.js && npm test -- firebase/functions/authTriggers.test.js` doit être vert.
- Le temps d’exécution total doit être < 120 s.
