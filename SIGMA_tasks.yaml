- task_id: ATELIER_ROLES_PERMISSIONS
  sprint: 0
  priority: critical
  kind: planning
  description: Atelier rôles/permissions
  status: todo
  target_files: ["docs/Permissions.md"]
  acceptance_test_cmd: "Revue manuelle"

- task_id: GEL_DU_MODELE_DE_DONNEES_V0_9
  sprint: 0
  priority: critical
  kind: documentation
  description: Gel du modèle de données v0.9
  status: todo
  target_files: ["docs/DataModel.md"]
  acceptance_test_cmd: "Revue manuelle"

- task_id: STORYMAPPING_UI
  sprint: 0
  priority: opportunity
  kind: planning
  description: Story-mapping UI
  status: todo
  target_files: ["docs/Interface_SIGMA_v1.0.md"]
  acceptance_test_cmd: "Revue manuelle"

- task_id: ECRIRE_FIRESTORE_RULES
  sprint: 1
  priority: critical
  kind: firestore_rules
  description: Écrire firestore.rules
  status: todo
  target_files: ["firebase/firestore.rules"]
  acceptance_test_cmd: "npm run test:rules"

- task_id: TESTS_SIMULATEUR
  sprint: 1
  priority: critical
  kind: tests
  description: Tests simulateur
  status: todo
  target_files: ["firebase/tests/rules.test.js"]
  acceptance_test_cmd: "npm run test:rules"

- task_id: ACTIVER_AUTH_GOOGLE
  sprint: 1
  priority: important
  kind: configuration
  description: Activer Auth (Google)
  status: done
  target_files: ["firebase.json"]
  acceptance_test_cmd: "Déploiement et test manuel"

- task_id: CF_SETUSERROLE_CALLABLE
  sprint: 1
  priority: critical
  kind: cloud_function
  description: CF setUserRole (Callable)
  status: todo
  target_files: ["firebase/functions/userManagement.js", "firebase/functions/userManagement.test.js"]
  acceptance_test_cmd: "npm test -- firebase/functions/userManagement.test.js"

- task_id: CF_ASSIGNDEFAULTROLEONCREATEUSER_TRIGGER
  sprint: 1
  priority: critical
  kind: cloud_function
  description: CF assignDefaultRoleOnCreateUser (Trigger)
  status: todo
  target_files: ["firebase/functions/authTriggers.js", "firebase/functions/authTriggers.test.js"]
  acceptance_test_cmd: "npm test -- firebase/functions/authTriggers.test.js"

- task_id: PIPELINE_CI_CD_LINTTESTSDEPLOY_DEV
  sprint: 1
  priority: critical
  kind: ci_cd
  description: Pipeline CI/CD lint-tests-deploy dev
  status: todo
  target_files: [".github/workflows/main.yml"]
  acceptance_test_cmd: "Déclenchement et validation manuelle du workflow sur GitHub"

- task_id: CONFIGURER_SECRETS_GITHUB
  sprint: 1
  priority: opportunity
  kind: ci_cd
  description: Configurer Secrets GitHub
  status: todo
  target_files: ["Configuration sur l'interface GitHub"]
  acceptance_test_cmd: "Revue manuelle"

- task_id: UI_ADMIN_USERS_HTML_USERS_JS
  sprint: 1
  priority: critical
  kind: ui
  description: UI admin/users.html + users.js
  status: todo
  target_files: ["src/html/admin/users.html", "src/js/admin/users.js"]
  acceptance_test_cmd: "clasp push && Test manuel de l'interface"

- task_id: AUDIT_SPRINT_0_1
  sprint: 2
  priority: critical
  kind: tests
  description: "Audit de vérification des fondations (Sprints 0 & 1)"
  status: todo
  target_files: ["rapport-audit.md"]
  acceptance_test_cmd: "npm run test:rules && npm test -- firebase/functions/userManagement.test.js && npm test -- firebase/functions/authTriggers.test.js"

- task_id: CF_CREATEEMPRUNTCF_CALLABLE
  sprint: 3
  priority: critical
  kind: cloud_function
  description: CF createEmpruntCF (Callable)
  status: done
  target_files:
    - "firebase/functions/emprunts.js"
    - "firebase/functions/emprunts.test.js"
  acceptance_test_cmd: "npm test -- firebase/functions/emprunts.test.js"

- task_id: CF_TRIGGEREMPRUNTDEPARTCF_CALLABLE
  sprint: 3
  priority: critical
  kind: cloud_function
  description: CF triggerEmpruntDepartCF (Callable)
  status: todo
  target_files: ["firebase/functions/emprunts.js", "firebase/functions/emprunts.test.js"]
  acceptance_test_cmd: "npm test -- firebase/functions/emprunts.test.js"

- task_id: CF_TRIGGEREMPRUNTRETOURCF_CALLABLE
  sprint: 3
  priority: critical
  kind: cloud_function
  description: CF triggerEmpruntRetourCF (Callable)
  status: todo
  target_files: ["firebase/functions/emprunts.js", "firebase/functions/emprunts.test.js"]
  acceptance_test_cmd: "npm test -- firebase/functions/emprunts.test.js"

- task_id: INDEX_COMPOSITES_FIRESTORE_EMPRUNTS
  sprint: 3
  priority: important
  kind: firestore
  description: Index composites Firestore emprunts
  status: todo
  target_files: ["firebase/firestore.indexes.json"]
  acceptance_test_cmd: "firebase deploy --only firestore:indexes"

- task_id: LISTENER_ALERTES_STOCK
  sprint: 3
  priority: critical
  kind: ui
  description: Listener Alertes stock
  status: todo
  target_files: ["src/js/domain/dashboardData.js", "src/js/views/dashboardView.js"]
  acceptance_test_cmd: "clasp push && Test manuel de l'interface"

- task_id: LISTENER_EMPRUNTS_RETARD
  sprint: 3
  priority: critical
  kind: ui
  description: Listener Emprunts retard
  status: todo
  target_files: ["src/js/domain/dashboardData.js", "src/js/views/dashboardView.js"]
  acceptance_test_cmd: "clasp push && Test manuel de l'interface"

- task_id: OPTIMISER_REQUETES_DASHBOARD
  sprint: 3
  priority: opportunity
  kind: optimization
  description: Optimiser requêtes Dashboard
  status: todo
  target_files: ["src/js/domain/dashboardData.js", "firebase/firestore.indexes.json"]
  acceptance_test_cmd: "Test de performance manuel et analyse Firestore"

- task_id: UI_FORMULAIRE_CREATION_MULTIETAPES
  sprint: 5
  priority: critical
  kind: ui
  description: UI Formulaire création multi-étapes
  status: todo
  target_files: ["src/html/emprunts.html", "src/js/views/empruntsView.js"]
  acceptance_test_cmd: "clasp push && Test manuel de l'interface"

- task_id: PDF_CONFIRMATION_GENERATELABELPDFCF
  sprint: 5
  priority: critical
  kind: cloud_function
  description: PDF confirmation generateLabelPDFCF
  status: todo
  target_files: ["firebase/functions/pdf.js", "firebase/functions/pdf.test.js"]
  acceptance_test_cmd: "npm test -- firebase/functions/pdf.test.js"

- task_id: TESTS_CYPRESS_FLUX_EMPRUNT_COMPLET
  sprint: 5
  priority: important
  kind: tests
  description: Tests Cypress flux emprunt complet
  status: todo
  target_files: ["cypress/e2e/emprunt.cy.js"]
  acceptance_test_cmd: "npx cypress run --spec cypress/e2e/emprunt.cy.js"

- task_id: CF_SAVEMODULECF_TRANSACTION
  sprint: 7
  priority: critical
  kind: cloud_function
  description: CF saveModuleCF (transaction)
  status: todo
  target_files: ["firebase/functions/modules.js", "firebase/functions/modules.test.js"]
  acceptance_test_cmd: "npm test -- firebase/functions/modules.test.js"

- task_id: CF_DISMANTLEMODULECF
  sprint: 7
  priority: critical
  kind: cloud_function
  description: CF dismantleModuleCF
  status: todo
  target_files: ["firebase/functions/modules.js", "firebase/functions/modules.test.js"]
  acceptance_test_cmd: "npm test -- firebase/functions/modules.test.js"

- task_id: TRIGGER_CHECKMODULEREADINESSONUPDATECF
  sprint: 7
  priority: important
  kind: cloud_function
  description: Trigger checkModuleReadinessOnUpdateCF
  status: todo
  target_files: ["firebase/functions/modules.js", "firebase/functions/modules.test.js"]
  acceptance_test_cmd: "npm test -- firebase/functions/modules.test.js"

- task_id: COUVERTURE_TESTS_CF_70
  sprint: 7
  priority: important
  kind: tests
  description: Couverture tests CF ≥ 70%
  status: todo
  target_files: ["rapport-de-couverture.txt"]
  acceptance_test_cmd: "npm test -- --coverage"

- task_id: CF_RECORDSTOCKMOVEMENTCF_TRANSACTION
  sprint: 9
  priority: critical
  kind: cloud_function
  description: CF recordStockMovementCF (transaction)
  status: todo
  target_files: ["firebase/functions/stocks.js", "firebase/functions/stocks.test.js"]
  acceptance_test_cmd: "npm test -- firebase/functions/stocks.test.js"

- task_id: ALERTES_SEUIL_STOCK_IN_TRANSACTION
  sprint: 9
  priority: critical
  kind: cloud_function
  description: Alertes seuil stock in-transaction
  status: todo
  target_files: ["firebase/functions/stocks.js", "firebase/functions/stocks.test.js"]
  acceptance_test_cmd: "npm test -- firebase/functions/stocks.test.js"

- task_id: UI_LISTE_FILTRES_STOCKS
  sprint: 9
  priority: critical
  kind: ui
  description: UI Liste & filtres Stocks
  status: todo
  target_files: ["src/html/stocks.html", "src/js/views/stocksView.js"]
  acceptance_test_cmd: "clasp push && Test manuel de l'interface"

- task_id: CF_SCHEDULED_BACKUPFIRESTORETOGCS
  sprint: 9
  priority: critical
  kind: cloud_function
  description: CF Scheduled backupFirestoreToGCS
  status: todo
  target_files: ["firebase/functions/backup.js"]
  acceptance_test_cmd: "Revue manuelle et test de déploiement"

- task_id: SCRIPT_RESTAURATION_TEST_MENSUELLE
  sprint: 9
  priority: important
  kind: scripts
  description: Script restauration test mensuelle
  status: todo
  target_files: ["scripts/restore_from_backup.sh"]
  acceptance_test_cmd: "Exécution manuelle en environnement de test"

- task_id: CF_PLANDELIVERYCF
  sprint: 11
  priority: critical
  kind: cloud_function
  description: CF planDeliveryCF
  status: todo
  target_files: ["firebase/functions/livraisons.js", "firebase/functions/livraisons.test.js"]
  acceptance_test_cmd: "npm test -- firebase/functions/livraisons.test.js"

- task_id: UI_CARTE_LEAFLET_LIVRAISONS
  sprint: 11
  priority: critical
  kind: ui
  description: UI Carte Leaflet livraisons
  status: todo
  target_files: ["src/html/livraisons.html", "src/js/views/livraisonsView.js"]
  acceptance_test_cmd: "clasp push && Test manuel de l'interface"

- task_id: UPLOAD_PREUVES_STORAGE_REGLES
  sprint: 11
  priority: important
  kind: storage
  description: Upload preuves Storage + règles
  status: todo
  target_files: ["firebase/storage.rules"]
  acceptance_test_cmd: "Revue manuelle et tests d'upload"

- task_id: DASHBOARD_CLOUD_MONITORING
  sprint: 11
  priority: critical
  kind: monitoring
  description: Dashboard Cloud Monitoring
  status: todo
  target_files: ["Configuration sur l'interface GCP"]
  acceptance_test_cmd: "Revue manuelle"

- task_id: ALERTES_LATENCE_500_MS
  sprint: 11
  priority: critical
  kind: monitoring
  description: Alertes latence >500 ms
  status: todo
  target_files: ["Configuration sur l'interface GCP"]
  acceptance_test_cmd: "Revue manuelle"

- task_id: AUDIT_OWASP_PENTEST
  sprint: 13
  priority: critical
  kind: security
  description: Audit OWASP + pentest
  status: todo
  target_files: ["rapport-audit.pdf"]
  acceptance_test_cmd: "Revue manuelle"

- task_id: TESTS_CHARGE_ARTILLERY
  sprint: 13
  priority: critical
  kind: tests
  description: Tests charge Artillery
  status: todo
  target_files: ["tests/charge/artillery.yml"]
  acceptance_test_cmd: "npx artillery run tests/charge/artillery.yml"

- task_id: ACCESSIBILITE_WCAG_AA
  sprint: 13
  priority: important
  kind: accessibility
  description: Accessibilité WCAG AA
  status: todo
  target_files: ["rapport-accessibilite.md"]
  acceptance_test_cmd: "Revue manuelle avec un outil d'audit"

- task_id: PILOTE_5_UTILISATEURS
  sprint: 13
  priority: important
  kind: pilot
  description: Pilote 5 utilisateurs
  status: todo
  target_files: ["feedback-pilote.md"]
  acceptance_test_cmd: "Revue manuelle"

- task_id: CONGELATION_CODE_TAG_V1_0_0
  sprint: 15
  priority: critical
  kind: release
  description: Congélation code tag v1.0.0
  status: todo
  target_files: ["git tag v1.0.0"]
  acceptance_test_cmd: "Revue manuelle"

- task_id: CHECKLIST_PREPROD_ROLLBACK_TESTE
  sprint: 15
  priority: critical
  kind: release
  description: Checklist pré-prod + rollback testé
  status: todo
  target_files: ["docs/release-checklist.md"]
  acceptance_test_cmd: "Revue manuelle"

- task_id: SMOKE_TESTS_PROD
  sprint: 15
  priority: important
  kind: tests
  description: Smoke tests prod
  status: todo
  target_files: ["tests/smoke/prod.js"]
  acceptance_test_cmd: "Exécution manuelle sur l'environnement de production"

- task_id: COLLECTE_FEEDBACK_UTILISATEURS
  sprint: 16
  priority: critical
  kind: feedback
  description: Collecte feedback utilisateurs
  status: todo
  target_files: ["feedback-v1.md"]
  acceptance_test_cmd: "Revue manuelle"

- task_id: PLANIFICATION_ROADMAP_V1_1
  sprint: 16
  priority: critical
  kind: planning
  description: Planification Roadmap v1.1
  status: todo
  target_files: ["docs/roadmap-v1.1.md"]
  acceptance_test_cmd: "Revue manuelle"

- task_id: AMELIORATION_ONBOARDING_DOCS
  sprint: 16
  priority: opportunity
  kind: documentation
  description: Amélioration onboarding & docs
  status: todo
  target_files: ["docs/onboarding.md"]
  acceptance_test_cmd: "Revue manuelle"